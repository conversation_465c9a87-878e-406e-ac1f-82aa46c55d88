<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScheduleConfig extends Model
{
    protected $table = 'schedule_configs';

    protected $fillable = [
        'tournament_id',
        'location_id',
        'begin_date',
        'begin_time',
        'end_time',
        'match_duration',
        'break_match_duration',
        'is_locked',
    ];

    protected $casts = [
        'begin_date' => 'datetime',
        'begin_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'is_locked' => 'boolean',
    ];

    public function tournament(): BelongsTo
    {
        return $this->belongsTo(Tournament::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }
}
