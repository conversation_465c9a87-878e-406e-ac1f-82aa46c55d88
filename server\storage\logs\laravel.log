[2025-06-26 04:13:53] development.ERROR: error in getScheduleConfigById ["[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse '2025-06-25 00:00:00 2025-06-26 18:00:00': Failed to parse time string (2025-06-25 00:00:00 2025-06-26 18:00:00) at position 20 (2): Double date specification at D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php:198)
[stacktrace]
#0 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(224): Carbon\\Carbon::rawParse('2025-06-25 00:0...', 'UTC')
#1 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Controllers\\ScheduleConfigController.php(187): Carbon\\Carbon::parse('2025-06-25 00:0...', 'UTC')
#2 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ScheduleConfigController->getScheduleConfigById('293')
#3 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getScheduleConf...', Array)
#4 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ScheduleConfigController), 'getScheduleConf...')
#5 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Middleware\\LocalizationMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LocalizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#18 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '120', '1')
#19 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Code\\\\Work\\\\ez...')
#45 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (2025-06-25 00:00:00 2025-06-26 18:00:00) at position 20 (2): Double date specification at D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php:91)
[stacktrace]
#0 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(187): Carbon\\Carbon->__construct('2025-06-25 00:0...', 'UTC')
#1 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(224): Carbon\\Carbon::rawParse('2025-06-25 00:0...', 'UTC')
#2 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Controllers\\ScheduleConfigController.php(187): Carbon\\Carbon::parse('2025-06-25 00:0...', 'UTC')
#3 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ScheduleConfigController->getScheduleConfigById('293')
#4 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getScheduleConf...', Array)
#5 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ScheduleConfigController), 'getScheduleConf...')
#6 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#8 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Middleware\\LocalizationMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LocalizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#15 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#16 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#19 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '120', '1')
#20 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Code\\\\Work\\\\ez...')
#46 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (2025-06-25 00:00:00 2025-06-26 18:00:00) at position 20 (2): Double date specification at D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php:89)
[stacktrace]
#0 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(89): DateTime->__construct('2025-06-25 00:0...', Object(Carbon\\CarbonTimeZone))
#1 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(187): Carbon\\Carbon->__construct('2025-06-25 00:0...', 'UTC')
#2 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(224): Carbon\\Carbon::rawParse('2025-06-25 00:0...', 'UTC')
#3 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Controllers\\ScheduleConfigController.php(187): Carbon\\Carbon::parse('2025-06-25 00:0...', 'UTC')
#4 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ScheduleConfigController->getScheduleConfigById('293')
#5 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getScheduleConf...', Array)
#6 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ScheduleConfigController), 'getScheduleConf...')
#7 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Middleware\\LocalizationMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LocalizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#16 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#17 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#18 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#20 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '120', '1')
#21 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Code\\\\Work\\\\ez...')
#47 {main}
"] 
[2025-06-26 04:14:33] development.ERROR: error in getScheduleConfigById ["[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse '2025-06-25 2025-06-26': Failed to parse time string (2025-06-25 2025-06-26) at position 11 (2): Double date specification at D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php:198)
[stacktrace]
#0 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(224): Carbon\\Carbon::rawParse('2025-06-25 2025...', 'UTC')
#1 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Controllers\\ScheduleConfigController.php(187): Carbon\\Carbon::parse('2025-06-25 2025...', 'UTC')
#2 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ScheduleConfigController->getScheduleConfigById('293')
#3 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getScheduleConf...', Array)
#4 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ScheduleConfigController), 'getScheduleConf...')
#5 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Middleware\\LocalizationMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LocalizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#18 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '120', '1')
#19 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Code\\\\Work\\\\ez...')
#45 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (2025-06-25 2025-06-26) at position 11 (2): Double date specification at D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php:91)
[stacktrace]
#0 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(187): Carbon\\Carbon->__construct('2025-06-25 2025...', 'UTC')
#1 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(224): Carbon\\Carbon::rawParse('2025-06-25 2025...', 'UTC')
#2 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Controllers\\ScheduleConfigController.php(187): Carbon\\Carbon::parse('2025-06-25 2025...', 'UTC')
#3 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ScheduleConfigController->getScheduleConfigById('293')
#4 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getScheduleConf...', Array)
#5 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ScheduleConfigController), 'getScheduleConf...')
#6 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#8 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Middleware\\LocalizationMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LocalizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#15 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#16 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#19 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '120', '1')
#20 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Code\\\\Work\\\\ez...')
#46 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (2025-06-25 2025-06-26) at position 11 (2): Double date specification at D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php:89)
[stacktrace]
#0 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(89): DateTime->__construct('2025-06-25 2025...', Object(Carbon\\CarbonTimeZone))
#1 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(187): Carbon\\Carbon->__construct('2025-06-25 2025...', 'UTC')
#2 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php(224): Carbon\\Carbon::rawParse('2025-06-25 2025...', 'UTC')
#3 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Controllers\\ScheduleConfigController.php(187): Carbon\\Carbon::parse('2025-06-25 2025...', 'UTC')
#4 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ScheduleConfigController->getScheduleConfigById('293')
#5 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getScheduleConf...', Array)
#6 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ScheduleConfigController), 'getScheduleConf...')
#7 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\app\\Http\\Middleware\\LocalizationMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LocalizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#16 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#17 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#18 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#20 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '120', '1')
#21 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 D:\\Code\\Work\\ezactive-vn\\ezleague-core\\server\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Code\\\\Work\\\\ez...')
#47 {main}
"] 
